import { type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData, Link as RemixLink } from "@remix-run/react";
import { Layout, Page, BlockStack, Text } from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { usePollingWithFeedback } from "app/hooks/usePollingWithFeedback";
import { MetricGrid, useDashboardMetrics } from "../components/DashboardMetrics";
import { JobGrid, EmptyJobs } from "../components/JobCard";
import { WelcomeBanner, QuickStart, useWelcomeExperience } from "../components/WelcomeExperience";
import { PlayIcon, BookIcon, SettingsIcon } from "@shopify/polaris-icons";


// --- REMIX LOADER ---
// Fetches all the data needed for the dashboard from the database.
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const { shop } = session;

  // Calculate the date 30 days ago for the "Total Runs" metric
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  // Fetch all data points concurrently for better performance
  const [activeAutomations, totalRuns, recentActivity] = await Promise.all([
    // 1. Get count of active automations
    prisma.automation.count({
      where: { shop, status: "Active" },
    }),
    // 2. Get count of all jobs run in the last 30 days
    prisma.job.count({
      where: {
        shop,
        createdAt: { gte: thirtyDaysAgo },
      },
    }),
    // 3. Get the 5 most recent job activities
    prisma.job.findMany({
      where: { shop },
      orderBy: { createdAt: "desc" },
      take: 5,
    }),
  ]);

  return Response.json({
    stats: { activeAutomations, totalRuns },
    recentActivity,
  });
};

export default function Dashboard() {
  const { stats, recentActivity } = useLoaderData<typeof loader>();

  // 🦠 BACTERIAL HOOKS - Clean state management
  const { metrics } = useDashboardMetrics(stats);
  const { showWelcome, dismissWelcome, isStepCompleted } = useWelcomeExperience();

  // 🦠 BACTERIAL POLLING - Auto-refresh
  usePollingWithFeedback(30000);

  // 🦠 BACTERIAL QUICK START STEPS - Onboarding guidance
  const quickStartSteps = [
    {
      title: "Browse Task Library",
      description: "Explore pre-built automation templates for common store tasks",
      icon: BookIcon,
      action: { content: "Browse Library", url: "/app/library" },
      completed: isStepCompleted("browse_library")
    },
    {
      title: "Create Your First Automation",
      description: "Set up an automation to start saving time on repetitive tasks",
      icon: PlayIcon,
      action: { content: "View Automations", url: "/app/automations" },
      completed: isStepCompleted("first_automation")
    },
    {
      title: "Monitor Job Activity",
      description: "Check the status and results of your automated tasks",
      icon: SettingsIcon,
      action: { content: "View Jobs", url: "/app/jobs" },
      completed: isStepCompleted("monitor_jobs")
    }
  ];

  return (
    <Page title="Dashboard">
      <Layout>
        {/* 🦠 BACTERIAL WELCOME BANNER */}
        {showWelcome && (
          <Layout.Section>
            <WelcomeBanner
              title="Welcome to the Automation Engine"
              message="Automate repetitive tasks in your store with our pre-built workflows. Get started by browsing the Task Library."
              primaryAction={{ content: "Browse Task Library", url: "/app/library" }}
              onDismiss={dismissWelcome}
              storageKey="welcomeBannerDismissed"
            />
          </Layout.Section>
        )}

        {/* 🦠 BACTERIAL METRICS GRID */}
        <Layout.Section>
          <BlockStack gap="500">
            <Text variant="headingXl" as="h2">Key Metrics</Text>
            <MetricGrid metrics={metrics} columns={2} enhanced={true} />
          </BlockStack>
        </Layout.Section>

        {/* 🦠 BACTERIAL QUICK START GUIDE */}
        <Layout.Section>
          <QuickStart
            steps={quickStartSteps}
            showProgress={true}
            enhanced={true}
          />
        </Layout.Section>

        {/* 🦠 BACTERIAL RECENT ACTIVITY - Reusing JobGrid component */}
        <Layout.Section>
          <BlockStack gap="500">
            <Text variant="headingXl" as="h2">Recent Activity</Text>
            <JobGrid
              jobs={recentActivity.slice(0, 5)}
              showEmptyState={true}
              emptyMessage="When your automations run, you'll see the latest activity here."
              enhanced={true}
              LinkComponent={RemixLink}
            />
          </BlockStack>
        </Layout.Section>
      </Layout>

      {/* 🦠 BACTERIAL SPACING */}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}