import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useF<PERSON>cher, useLoaderData, useSearchParams, useSubmit, Link as RemixLink } from "@remix-run/react";
import { <PERSON>ton, Card, Grid, Layout, Page, Text, TextField, Tabs, BlockStack, InlineStack, Icon, Spinner } from "@shopify/polaris";
import { SearchIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { useEffect, useState, useRef, useMemo } from "react";
import type { JobType } from "@prisma/client";
import { TASKS_LIBRARY } from "../data/tasks";
import { NumberedPagination } from "../components/NumberedPagination";
import {
  extractSearchParams,
  createSearchFormData,
  calculatePagination,
  createSearchSummary,
  hasActiveFilters
} from "../utils/search";
import {
  createTaskTemplateFindOptions,
  createTaskTemplateCountOptions,
  transformDbTasksToDisplay,
  extractUniqueValues,
  needsSeeding,
  createAutomationLookupSet,
  isTaskInstalled
} from "../utils/database-query";
import {
  LIBRARY_TABS,
  createTabState,
  createTabChangeHandler
} from "../utils/tabs";


// Define the shape of a task as it's used in the component and returned by the loader
type DisplayTask = {
  id: number;
  title: string;
  description: string;
  category: string;
  type: JobType;
  trigger: string;
};

// Define the overall shape of the data returned by the loader
type LoaderData = {
  tasks: DisplayTask[];
  installedTypes: JobType[];
  needsSeeding: boolean;
  currentPage: number;
  totalPages: number;
};

// --- REMIX LOADER ---
// Fetches available tasks and checks which ones are already installed.
export const loader = async ({ request }: LoaderFunctionArgs): Promise<Response> => {
    const { session } = await authenticate.admin(request);
    const url = new URL(request.url);

    // Use bacterial utility to extract search parameters
    const searchConfig = extractSearchParams(url);

    // Check if seeding is needed using bacterial utility
    const taskTemplateCount = await prisma.taskTemplate.count();
    const seedingNeeded = needsSeeding(TASKS_LIBRARY.length, taskTemplateCount);

    // Use bacterial utilities to build database queries
    const findOptions = createTaskTemplateFindOptions(searchConfig);
    const countOptions = createTaskTemplateCountOptions(searchConfig);

    // Fetch task templates from the database with pagination
    const [availableTasksFromDB, totalTasks] = await Promise.all([
      prisma.taskTemplate.findMany(findOptions),
      prisma.taskTemplate.count(countOptions)
    ]);

    // Use bacterial utility to calculate pagination
    const pagination = calculatePagination(totalTasks, searchConfig.pageSize!, searchConfig.page!);

    // Use bacterial utility to transform database tasks to display tasks
    const availableTasks: DisplayTask[] = transformDbTasksToDisplay(availableTasksFromDB);

    // Get the types of automations the user has already installed
    const installedAutomations = await prisma.automation.findMany({
        where: { shop: session.shop },
        select: { type: true },
    });

    // Use bacterial utility to extract unique values
    const installedTypes = extractUniqueValues(installedAutomations, 'type');

    const data: LoaderData = {
      tasks: availableTasks,
      installedTypes,
      needsSeeding: seedingNeeded,
      currentPage: searchConfig.page!,
      totalPages: pagination.totalPages
    };
    return Response.json(data);
};

export const action = async ({ request }: ActionFunctionArgs) => {
    const formData = await request.formData();
    const { _action } = Object.fromEntries(formData);

    if (_action === "seedTaskTemplates") {
        for (const task of TASKS_LIBRARY) {
            // Destructure to separate model fields from extra fields like optionsSchema
            const { optionsSchema, ...taskTemplateData } = task;

            await prisma.taskTemplate.upsert({
                // Use the 'type' field as the unique identifier for the lookup.
                // This requires a @unique constraint on the 'type' field in your schema.
                where: { type: task.type }, 
                
                // If a record with this type is found, update it.
                update: {
                    ...taskTemplateData,
                },
                // If no record is found, create a new one.
                create: {
                    ...taskTemplateData,
                },
            });
        }
        return Response.json({ ok: true, message: "Task templates seeded." });
    }
    return Response.json({ ok: false, error: "Invalid action" }, { status: 400 });
};

export default function TaskLibrary() {
  const { tasks, installedTypes, needsSeeding, currentPage, totalPages } = useLoaderData<LoaderData>();
  const [searchParams] = useSearchParams();
  const submit = useSubmit();
  const fetcher = useFetcher();

  useEffect(() => {
    if (needsSeeding && fetcher.state === 'idle') {
      console.log("Task templates need seeding. Initiating seed...");
      fetcher.submit({ _action: "seedTaskTemplates" }, { method: "post" });
    }
  }, [needsSeeding, fetcher, tasks.length]);

  const queryValue = searchParams.get("query") || "";
  const selectedCategory = searchParams.get("category") || "all";

  // Local state for immediate UI updates
  const [searchInput, setSearchInput] = useState(queryValue);
  const [isSearching, setIsSearching] = useState(false);

  // Sync local state with URL params when they change
  useEffect(() => {
    setSearchInput(queryValue);
    setIsSearching(false); // Reset loading state when search completes
  }, [queryValue]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Cmd/Ctrl + K to focus search
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault();
        const searchField = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement;
        if (searchField) {
          searchField.focus();
          searchField.select();
        }
      }

      // Escape to clear search when focused on search field
      if (event.key === 'Escape' && event.target instanceof HTMLInputElement &&
          event.target.placeholder?.includes('Search')) {
        clearSearch();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Simple timeout-based debounce without external dependencies
  const timeoutRef = useRef<NodeJS.Timeout>();

  const performSearch = (value: string, category: string) => {
    // Clear existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set new timeout
    timeoutRef.current = setTimeout(() => {
      setIsSearching(true);
      const formData = createSearchFormData({
        query: value,
        category: category,
        page: 1
      });
      submit(formData, { method: "get", replace: true });
    }, 300);
  };

  // Clear search function
  const clearSearch = () => {
    setSearchInput("");
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsSearching(true);
    const formData = createSearchFormData({
      query: "",
      category: selectedCategory,
      page: 1
    });
    submit(formData, { method: "get", replace: true });
  };

  // Use bacterial utility to create tab state
  const tabState = useMemo(() =>
    createTabState(LIBRARY_TABS, selectedCategory),
    [selectedCategory]
  );

  // Use bacterial utility to create tab change handler
  const handleTabChange = createTabChangeHandler(LIBRARY_TABS, (newCategory) => {
    const formData = createSearchFormData({
      query: queryValue,
      category: newCategory,
      page: 1
    });
    submit(formData, { method: 'get', replace: true });
  });

  const handlePageChange = (newPage: number) => {
    const formData = createSearchFormData({
      query: queryValue,
      category: selectedCategory,
      page: newPage
    });
    submit(formData, { method: 'get', replace: true });
  }

  // Use bacterial utility to create automation lookup set
  const installedSet = createAutomationLookupSet(installedTypes.map(type => ({ type })));

  // Display a message while seeding or if seeding failed and tasks are empty
  if (fetcher.state !== 'idle' && tasks.length === 0) {
    return (
        <Page title="Task Library">
            <Layout>
                <Layout.Section>
                    <Card>
                        <Text variant="bodyMd" as="p">Seeding task templates... Please wait.</Text>
                    </Card>
                </Layout.Section>
            </Layout>
        </Page>
    );
  }

  return (
    <Page title="Task Library">
      <Layout>
        <Layout.Section>
          <BlockStack gap="300">
            <div style={{
              transition: 'all 0.2s ease',
              transform: isSearching ? 'scale(1.01)' : 'scale(1)',
              opacity: isSearching ? 0.8 : 1
            }}>
              <TextField
                label="Search Tasks"
                labelHidden
                value={searchInput}
                onChange={(value) => {
                  // Update local state immediately for responsive UI
                  setSearchInput(value);
                  // Use debounced search to prevent excessive API calls
                  performSearch(value, selectedCategory);
                }}
                placeholder="Search for automation tasks... (⌘K)"
                autoComplete="off"
                prefix={<Icon source={SearchIcon} />}
                clearButton={searchInput ? true : false}
                onClearButtonClick={clearSearch}
                helpText={searchInput && !isSearching ? `Press Escape to clear search` : undefined}
              />
            </div>

            {/* Search Results Summary and Clear Filters */}
            <InlineStack align="space-between" blockAlign="center">
              <InlineStack gap="200" blockAlign="center">
                {isSearching && <Spinner size="small" />}
                <Text variant="bodySm" tone="subdued" as="p">
                  {(searchInput || selectedCategory !== "all")
                    ? createSearchSummary({ query: searchInput, category: selectedCategory }, tasks.length)
                    : `${tasks.length} automation tasks available`
                  }
                </Text>
              </InlineStack>

              {hasActiveFilters({ query: searchInput, category: selectedCategory }) && (
                <Button
                  onClick={() => {
                    setSearchInput("");
                    setIsSearching(true);
                    const formData = createSearchFormData({
                      query: "",
                      category: "all",
                      page: 1
                    });
                    submit(formData, { method: "get", replace: true });
                  }}
                  variant="plain"
                  size="micro"
                >
                  Clear all filters
                </Button>
              )}
            </InlineStack>
          </BlockStack>
        </Layout.Section>
        <Layout.Section>
          <Tabs
            tabs={LIBRARY_TABS}
            selected={tabState.selectedIndex}
            onSelect={handleTabChange}
          />
        </Layout.Section>
        <Layout.Section>
          <Grid>
            {tasks.map(task => {
              // Use bacterial utility to check if task is installed
              const taskInstalled = isTaskInstalled(task.type, installedSet);
              return (
              <Grid.Cell key={task.id} columnSpan={{xs: 6, sm: 3, md: 3, lg: 4, xl: 4}}>
                <Card>
                    <BlockStack gap="300">
                        <Text variant="headingMd" as="h2">{task.title}</Text>
                        <Text variant="bodyMd" as="p" tone="subdued">{task.description}</Text>
                        <RemixLink
                          to={taskInstalled ? "/app/automations" : `/app/automations/configure/${task.id}`}
                          style={{ textDecoration: 'none', display: 'block' }}
                        >
                            <Button
                                variant={taskInstalled ? "secondary" : "primary"}
                                fullWidth
                            >
                                {taskInstalled ? "Manage" : "Configure & Add"}
                            </Button>
                        </RemixLink>
                    </BlockStack>
                </Card>
              </Grid.Cell>
            )})}
          </Grid>
          {tasks.length === 0 && !needsSeeding && fetcher.state === 'idle' && (
            <Card>
                <Text variant="bodyMd" as="p">
                    No tasks found matching your criteria.
                </Text>
            </Card>
          )}
        </Layout.Section>
        {tasks.length > 0 && totalPages > 1 && (
          <Layout.Section>
            <InlineStack align="center">
              <NumberedPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </InlineStack>
          </Layout.Section>
        )}
      </Layout>
      {/* Add some spacing at the bottom of the page */}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}