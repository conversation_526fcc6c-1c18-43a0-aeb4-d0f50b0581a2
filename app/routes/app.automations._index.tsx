import { type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { useMemo, useState, useEffect } from "react";
import { useFetcher, useLoaderData, Link as RemixLink } from "@remix-run/react";
import { BlockStack, Layout, Page, Toast } from "@shopify/polaris";
import { JobType, type Automation } from "@prisma/client";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { usePollingWithFeedback } from "app/hooks/usePollingWithFeedback";
import { getAutomationsWithStatus } from "app/services/automations.server";
import { startBulkOperation } from "app/services/bulkOperations.server";
import { addJob } from "app/queues/jobQueue.server";
import { AutomationGrid, EmptyAutomations } from "../components/AutomationCard";
import { AutomationFilters, FilterSummary, useAutomationFilters, filterAutomations } from "../components/AutomationFilters";

// This is the type of data we get from the loader after JSON serialization
type SerializedAutomationWithStatus = Omit<Automation, 'createdAt' | 'updatedAt' | 'lastRunAt'> & {
  running: boolean;
  createdAt: string;
  updatedAt: string;
  lastRunAt: string | null;
};

// This is the type we use in the component after rehydrating the dates
type AutomationWithStatus = Automation & { running: boolean };

// LOADER
export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const automationsWithStatus = await getAutomationsWithStatus(session.shop);
  return Response.json({ automations: automationsWithStatus });
};

// ACTION
export const action = async ({ request }: ActionFunctionArgs) => {
  const { session, admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const { _action, id, status } = Object.fromEntries(formData);

  const automationId = parseInt(id as string, 10);
  if (isNaN(automationId)) {
    return Response.json({ error: "Invalid automation ID" }, { status: 400 });
  }

  const automation = await prisma.automation.findUnique({ where: { id: automationId, shop: session.shop } });
  if (!automation) {
    return Response.json({ error: "Automation not found" }, { status: 404 });
  }

  switch (_action) {
    case "toggleStatus":
      await prisma.automation.update({
        where: { id: automationId, shop: session.shop },
        data: { status: status as string },
      });
      return Response.json({ ok: true });

    

    case "runBulk":
      // Special handling for Collection Visibility. Instead of a Shopify Bulk Operation,
      // we enqueue a custom, collection-centric job.
      if (automation.type === JobType.COLLECTION_VISIBILITY_UPDATE) {
        await addJob({
          shop: session.shop,
          jobType: JobType.COLLECTION_VISIBILITY_BULK_UPDATE,
          data: { automationId: automation.id }, // Pass automationId for context if needed
        });
        await prisma.automation.update({
          where: { id: automationId },
          data: { lastRunAt: new Date() },
        });
        return Response.json({ ok: true, message: "Bulk collection visibility check has been scheduled." });
      }

      // Default handling for other bulk operations that use Shopify's bulk query runner.
      const result = await startBulkOperation(admin, automation);
      if (result.success) {
        await prisma.automation.update({
          where: { id: automationId },
          data: { lastRunAt: new Date() },
        });
        return Response.json({ ok: true, message: result.message });
      } else {
        return Response.json({ error: result.error }, { status: 500 });
      }

    default:
      return Response.json({ error: "Invalid action" }, { status: 400 });
  }
}

export default function Automations() {
  // 🦠 BACTERIAL DATA LOADING - Clean data rehydration
  const loaderData = useLoaderData<{ automations: SerializedAutomationWithStatus[] }>();
  const automations: AutomationWithStatus[] = useMemo(() => {
    return loaderData.automations.map(auto => ({
      ...auto,
      createdAt: new Date(auto.createdAt),
      updatedAt: new Date(auto.updatedAt),
      lastRunAt: auto.lastRunAt ? new Date(auto.lastRunAt) : null,
    }));
  }, [loaderData.automations]);

  // 🦠 BACTERIAL STATE MANAGEMENT - Clean separation
  const fetcher = useFetcher<typeof action>();
  const { filters, setSearchQuery, setStatusFilters, setTypeFilters, setShowOnlyRunning, clearFilters } = useAutomationFilters();

  usePollingWithFeedback(30000);

  // Toast state
  const [toastContent, setToastContent] = useState("");
  const [isToastError, setIsToastError] = useState(false);
  const [toastActive, setToastActive] = useState(false);
  const [runningAutomationId, setRunningAutomationId] = useState<number | null>(null);

  // Effect to handle feedback from the fetcher
  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      if ('error' in fetcher.data && fetcher.data.error) {
        setToastContent(fetcher.data.error);
        setIsToastError(true);
        setToastActive(true);
      } else if ('message' in fetcher.data && fetcher.data.message) {
        setToastContent(fetcher.data.message);
        setIsToastError(false);
        setToastActive(true);
      }
      // Reset the running ID after the action is complete
      setRunningAutomationId(null);
    }
  }, [fetcher.data, fetcher.state]);

  // 🦠 BACTERIAL ACTION HANDLERS - Pure functions
  const handleToggle = (id: number, currentStatus: string) => {
    fetcher.submit(
      { _action: "toggleStatus", id: id.toString(), status: currentStatus === "Active" ? "Inactive" : "Active" },
      { method: "post" }
    );
  };

  const handleBulkRun = (id: number) => {
    setRunningAutomationId(id);
    fetcher.submit({ _action: "runBulk", id: id.toString() }, { method: "post" });
  };

  // 🦠 BACTERIAL DATA FILTERING - Pure computation
  const filteredAutomations = useMemo(() => {
    return filterAutomations(automations, filters);
  }, [automations, filters]);

  // 🦠 BACTERIAL AUTOMATION CARDS DATA - Transform for components
  const automationCards = filteredAutomations.map(automation => ({
    id: automation.id,
    name: automation.name,
    trigger: automation.trigger,
    status: automation.status,
    type: automation.type,
    lastRunAt: automation.lastRunAt,
    running: automation.running,
    config: (automation as any).config, // Type assertion for config field
    configureUrl: `/app/automations/configure/${automation.id}`,
    LinkComponent: RemixLink,
    onToggleStatus: handleToggle,
    onRunBulk: handleBulkRun,
    isStarting: fetcher.state !== 'idle' && runningAutomationId === automation.id,
    isActionInProgress: fetcher.state !== 'idle',
    enhanced: true
  }));

  const toastMarkup = toastActive ? (
    <Toast content={toastContent} onDismiss={() => setToastActive(false)} error={isToastError} />
  ) : null;

  return (
    <Page
      title="My Automations"
      primaryAction={{ content: "Browse Task Library", url: "/app/library" }}
    >
      <Layout>
        <Layout.Section>
          <BlockStack gap="400">
            {/* 🦠 BACTERIAL FILTERS */}
            <AutomationFilters
              searchQuery={filters.searchQuery}
              statusFilters={filters.statusFilters}
              typeFilters={filters.typeFilters}
              showOnlyRunning={filters.showOnlyRunning}
              onSearchChange={setSearchQuery}
              onStatusFilterChange={setStatusFilters}
              onTypeFilterChange={setTypeFilters}
              onRunningFilterChange={setShowOnlyRunning}
              onClearFilters={clearFilters}
            />

            {/* 🦠 BACTERIAL FILTER SUMMARY */}
            <FilterSummary
              totalCount={automations.length}
              filteredCount={filteredAutomations.length}
              filters={filters}
            />

            {/* 🦠 BACTERIAL AUTOMATION GRID OR EMPTY STATE */}
            {automationCards.length > 0 ? (
              <AutomationGrid automations={automationCards} enhanced={true} />
            ) : automations.length === 0 ? (
              <EmptyAutomations
                message="You haven't created any automations yet."
                actionText="Browse Task Library"
                actionUrl="/app/library"
                LinkComponent={RemixLink}
              />
            ) : (
              <EmptyAutomations
                message="No automations match your current filters."
                actionText="Clear filters"
                actionUrl="#"
                LinkComponent={({ children }: { children: any }) => (
                  <span onClick={clearFilters} style={{ cursor: 'pointer' }}>
                    {children}
                  </span>
                )}
              />
            )}
          </BlockStack>
        </Layout.Section>
      </Layout>
      {toastMarkup}
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}