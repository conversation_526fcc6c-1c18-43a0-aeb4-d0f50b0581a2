// 🦠 BACTERIAL AUTOMATION CONFIG FORM COMPONENTS
// Self-contained, reusable automation configuration components

import { useState, useCallback } from "react";
import { Card, BlockStack, Text, TextField, Select, FormLayout, InlineStack, Icon, Tooltip } from "@shopify/polaris";
import { ClockIcon, CalendarIcon, InfoIcon } from "@shopify/polaris-icons";
import type { JobType } from "@prisma/client";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface AutomationBasicInfoProps {
  /** Automation name */
  name: string;
  /** Name change handler */
  onNameChange: (name: string) => void;
  /** Automation trigger description */
  trigger: string;
  /** Automation description */
  description: string;
  /** Whether this is a new automation */
  isNew: boolean;
  /** Name validation error */
  nameError?: string;
}

export interface DelayConfig {
  type: 'immediate' | 'relative' | 'specific';
  value?: number;
  unit?: 'seconds' | 'minutes' | 'hours' | 'days';
  timestamp?: string;
}

export interface SchedulingConfigProps {
  /** Current delay configuration */
  delayConfig: DelayConfig;
  /** Delay configuration change handler */
  onDelayConfigChange: (config: DelayConfig) => void;
  /** Whether to show advanced options */
  showAdvanced?: boolean;
}

export interface ConfigFormProps {
  /** Basic automation info */
  basicInfo: AutomationBasicInfoProps;
  /** Scheduling configuration */
  scheduling: SchedulingConfigProps;
  /** Automation type */
  automationType: JobType;
  /** Current automation config */
  automationConfig: Record<string, any>;
  /** Config change handler */
  onConfigChange: (config: Record<string, any>) => void;
  /** Form submission handler */
  onSubmit: (data: FormData) => void;
  /** Whether form is submitting */
  isSubmitting?: boolean;
  /** General form errors */
  errors?: Record<string, string>;
}

// 🦠 BACTERIAL DELAY OPTIONS - Pure configuration
const DELAY_TYPE_OPTIONS = [
  { 
    label: 'Immediate', 
    value: 'immediate',
    helpText: 'Execute as soon as the trigger event occurs'
  },
  { 
    label: 'Delayed (Relative)', 
    value: 'relative',
    helpText: 'Execute after a specified amount of time'
  },
  { 
    label: 'Scheduled (Specific Time)', 
    value: 'specific',
    helpText: 'Execute at a specific date and time'
  }
];

const DELAY_UNIT_OPTIONS = [
  { label: 'Seconds', value: 'seconds' },
  { label: 'Minutes', value: 'minutes' },
  { label: 'Hours', value: 'hours' },
  { label: 'Days', value: 'days' }
];

// 🦠 BACTERIAL UTILITY - Format delay description
export function formatDelayDescription(config: DelayConfig): string {
  switch (config.type) {
    case 'immediate':
      return 'Executes immediately when triggered';
    case 'relative':
      if (config.value && config.unit) {
        const plural = config.value !== 1 ? 's' : '';
        return `Executes ${config.value} ${config.unit}${plural} after trigger`;
      }
      return 'Executes after specified delay';
    case 'specific':
      if (config.timestamp) {
        try {
          const date = new Date(config.timestamp);
          return `Executes at ${date.toLocaleString()}`;
        } catch {
          return 'Executes at specified time';
        }
      }
      return 'Executes at specified time';
    default:
      return 'Execution timing not configured';
  }
}

// 🦠 BACTERIAL UTILITY - Validate delay configuration
export function validateDelayConfig(config: DelayConfig): string | null {
  switch (config.type) {
    case 'relative':
      if (!config.value || config.value <= 0) {
        return 'Delay amount must be greater than 0';
      }
      if (!config.unit) {
        return 'Delay unit is required';
      }
      break;
    case 'specific':
      if (!config.timestamp) {
        return 'Scheduled time is required';
      }
      try {
        const date = new Date(config.timestamp);
        if (date <= new Date()) {
          return 'Scheduled time must be in the future';
        }
      } catch {
        return 'Invalid date format';
      }
      break;
  }
  return null;
}

// 🦠 BACTERIAL BASIC INFO COMPONENT - Reusable automation info
export function AutomationBasicInfo({
  name,
  onNameChange,
  trigger,
  description,
  isNew,
  nameError
}: AutomationBasicInfoProps) {
  return (
    <Card>
      <BlockStack gap="400">
        <Text variant="headingMd" as="h2">
          {isNew ? 'New Automation' : 'Edit Automation'}
        </Text>
        
        <TextField
          label="Automation Name"
          value={name}
          onChange={onNameChange}
          autoComplete="off"
          helpText="This name helps you identify the automation in your list."
          error={nameError}
          placeholder="Enter a descriptive name..."
        />
        
        <BlockStack gap="200">
          <InlineStack gap="100" blockAlign="center" wrap={false}>
            <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
              <Icon source={ClockIcon} tone="subdued" />
            </div>
            <Text variant="bodySm" tone="subdued" as="span">
              <strong>Trigger:</strong> {trigger}
            </Text>
          </InlineStack>
          
          <Text variant="bodySm" tone="subdued" as="p">
            <strong>Description:</strong> {description}
          </Text>
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL SCHEDULING COMPONENT - Reusable delay configuration
export function SchedulingConfig({
  delayConfig,
  onDelayConfigChange,
  showAdvanced = true
}: SchedulingConfigProps) {
  const [localConfig, setLocalConfig] = useState<DelayConfig>(delayConfig);
  
  const updateConfig = useCallback((updates: Partial<DelayConfig>) => {
    const newConfig = { ...localConfig, ...updates };
    setLocalConfig(newConfig);
    onDelayConfigChange(newConfig);
  }, [localConfig, onDelayConfigChange]);

  const selectedOption = DELAY_TYPE_OPTIONS.find(opt => opt.value === localConfig.type);
  const validationError = validateDelayConfig(localConfig);

  return (
    <Card>
      <BlockStack gap="400">
        <InlineStack gap="200" blockAlign="center">
          <Icon source={CalendarIcon} />
          <Text variant="headingMd" as="h2">
            Scheduling
          </Text>
          {showAdvanced && (
            <Tooltip content="Configure when this automation should execute">
              <Icon source={InfoIcon} tone="subdued" />
            </Tooltip>
          )}
        </InlineStack>
        
        <Select
          label="Execution Timing"
          options={DELAY_TYPE_OPTIONS.map(opt => ({ label: opt.label, value: opt.value }))}
          value={localConfig.type}
          onChange={(value) => updateConfig({ type: value as DelayConfig['type'] })}
          helpText={selectedOption?.helpText}
        />
        
        {localConfig.type === 'relative' && (
          <FormLayout.Group>
            <TextField
              label="Delay Amount"
              type="number"
              value={localConfig.value?.toString() || ''}
              onChange={(value) => updateConfig({ value: parseInt(value, 10) || 0 })}
              min="1"
              autoComplete="off"
              error={validationError && localConfig.type === 'relative' ? validationError : undefined}
            />
            <Select
              label="Delay Unit"
              options={DELAY_UNIT_OPTIONS}
              value={localConfig.unit || 'hours'}
              onChange={(value) => updateConfig({ unit: value as DelayConfig['unit'] })}
            />
          </FormLayout.Group>
        )}
        
        {localConfig.type === 'specific' && (
          <TextField
            label="Scheduled Date and Time"
            type="datetime-local"
            value={localConfig.timestamp || ''}
            onChange={(value) => updateConfig({ timestamp: value })}
            autoComplete="off"
            helpText="Select when this automation should execute"
            error={validationError && localConfig.type === 'specific' ? validationError : undefined}
          />
        )}
        
        {/* Delay preview */}
        <Card background="bg-surface-secondary">
          <Text variant="bodySm" tone="subdued" as="p">
            <strong>Preview:</strong> {formatDelayDescription(localConfig)}
          </Text>
        </Card>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL HOOK - Delay configuration state management
export function useDelayConfig(initialConfig?: DelayConfig) {
  const [delayConfig, setDelayConfig] = useState<DelayConfig>(
    initialConfig || { type: 'immediate' }
  );

  const updateDelayConfig = useCallback((updates: Partial<DelayConfig>) => {
    setDelayConfig(prev => ({ ...prev, ...updates }));
  }, []);

  const resetDelayConfig = useCallback(() => {
    setDelayConfig({ type: 'immediate' });
  }, []);

  const isValid = validateDelayConfig(delayConfig) === null;

  return {
    delayConfig,
    updateDelayConfig,
    resetDelayConfig,
    isValid,
    validationError: validateDelayConfig(delayConfig)
  };
}
