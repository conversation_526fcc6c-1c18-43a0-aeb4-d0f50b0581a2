// 🦠 BACTERIAL JOB ACTIONS COMPONENTS
// Self-contained, reusable job action controls

import { Card, BlockStack, InlineStack, Text, Button, ButtonGroup, Tooltip } from "@shopify/polaris";
import { RefreshIcon, XIcon, PlayIcon, ClockIcon } from "@shopify/polaris-icons";
import type { JobStatus } from "@prisma/client";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface JobActionProps {
  /** Job ID */
  jobId: string;
  /** Job status */
  status: JobStatus;
  /** Retry job handler */
  onRetry: (jobId: string) => void;
  /** Cancel job handler */
  onCancel: (jobId: string) => void;
  /** Refresh job handler */
  onRefresh?: () => void;
  /** Whether action is in progress */
  isActionInProgress?: boolean;
  /** Show enhanced actions */
  enhanced?: boolean;
}

export interface JobStatusActionsProps {
  /** Job status */
  status: JobStatus;
  /** Available actions */
  actions: {
    retry?: () => void;
    cancel?: () => void;
    refresh?: () => void;
  };
  /** Loading state */
  isLoading?: boolean;
  /** Show tooltips */
  showTooltips?: boolean;
}

// 🦠 BACTERIAL UTILITY - Get available actions for status
export function getAvailableActions(status: JobStatus): {
  canRetry: boolean;
  canCancel: boolean;
  canRefresh: boolean;
  primaryAction?: 'retry' | 'cancel' | 'refresh';
} {
  switch (status) {
    case 'failed':
      return {
        canRetry: true,
        canCancel: false,
        canRefresh: true,
        primaryAction: 'retry'
      };
    case 'processing':
    case 'retrying':
      return {
        canRetry: false,
        canCancel: true,
        canRefresh: true,
        primaryAction: 'cancel'
      };
    case 'pending':
    case 'scheduled':
      return {
        canRetry: false,
        canCancel: true,
        canRefresh: true,
        primaryAction: 'cancel'
      };
    case 'completed':
    case 'canceled':
      return {
        canRetry: false,
        canCancel: false,
        canRefresh: true,
        primaryAction: 'refresh'
      };
    default:
      return {
        canRetry: false,
        canCancel: false,
        canRefresh: true
      };
  }
}

// 🦠 BACTERIAL UTILITY - Get action button config
export function getActionButtonConfig(action: 'retry' | 'cancel' | 'refresh'): {
  label: string;
  icon: any;
  variant: "primary" | "secondary" | "tertiary";
  tone?: "critical" | "success";
  tooltip: string;
} {
  switch (action) {
    case 'retry':
      return {
        label: "Retry Job",
        icon: RefreshIcon,
        variant: "primary",
        tone: "success",
        tooltip: "Re-queue this job for processing"
      };
    case 'cancel':
      return {
        label: "Cancel Job",
        icon: XIcon,
        variant: "secondary",
        tone: "critical",
        tooltip: "Cancel this job and mark it as canceled"
      };
    case 'refresh':
      return {
        label: "Refresh",
        icon: RefreshIcon,
        variant: "tertiary",
        tooltip: "Refresh job details to get latest status"
      };
  }
}

// 🦠 BACTERIAL STATUS ACTIONS - Context-aware action buttons
export function JobStatusActions({
  status,
  actions,
  isLoading = false,
  showTooltips = true
}: JobStatusActionsProps) {
  const availableActions = getAvailableActions(status);
  const actionButtons = [];

  if (availableActions.canRetry && actions.retry) {
    const config = getActionButtonConfig('retry');
    actionButtons.push(
      <Tooltip key="retry" content={showTooltips ? config.tooltip : ""}>
        <Button
          variant={availableActions.primaryAction === 'retry' ? "primary" : config.variant}
          tone={config.tone}
          icon={config.icon}
          onClick={actions.retry}
          loading={isLoading}
          disabled={isLoading}
        >
          {config.label}
        </Button>
      </Tooltip>
    );
  }

  if (availableActions.canCancel && actions.cancel) {
    const config = getActionButtonConfig('cancel');
    actionButtons.push(
      <Tooltip key="cancel" content={showTooltips ? config.tooltip : ""}>
        <Button
          variant={availableActions.primaryAction === 'cancel' ? "primary" : config.variant}
          tone={config.tone}
          icon={config.icon}
          onClick={actions.cancel}
          loading={isLoading}
          disabled={isLoading}
        >
          {config.label}
        </Button>
      </Tooltip>
    );
  }

  if (availableActions.canRefresh && actions.refresh) {
    const config = getActionButtonConfig('refresh');
    actionButtons.push(
      <Tooltip key="refresh" content={showTooltips ? config.tooltip : ""}>
        <Button
          variant={config.variant}
          icon={config.icon}
          onClick={actions.refresh}
          loading={isLoading}
          disabled={isLoading}
        >
          {config.label}
        </Button>
      </Tooltip>
    );
  }

  if (actionButtons.length === 0) {
    return null;
  }

  return (
    <ButtonGroup>
      {actionButtons}
    </ButtonGroup>
  );
}

// 🦠 BACTERIAL JOB ACTIONS - Main actions component
export function JobActions({
  jobId,
  status,
  onRetry,
  onCancel,
  onRefresh,
  isActionInProgress = false,
  enhanced = true
}: JobActionProps) {
  const availableActions = getAvailableActions(status);

  const actions = {
    retry: availableActions.canRetry ? () => onRetry(jobId) : undefined,
    cancel: availableActions.canCancel ? () => onCancel(jobId) : undefined,
    refresh: onRefresh && availableActions.canRefresh ? onRefresh : undefined
  };

  return (
    <Card>
      <BlockStack gap="300">
        <Text variant="headingMd" as="h3">
          Actions
        </Text>
        
        <JobStatusActions
          status={status}
          actions={actions}
          isLoading={isActionInProgress}
          showTooltips={enhanced}
        />
        
        {enhanced && (
          <Card background="bg-surface-secondary">
            <BlockStack gap="200">
              <Text variant="bodySm" fontWeight="medium" as="h4">
                Available Actions
              </Text>
              <BlockStack gap="100">
                {availableActions.canRetry && (
                  <InlineStack gap="100" blockAlign="center">
                    <RefreshIcon />
                    <Text variant="bodySm" tone="subdued" as="span">
                      Retry: Re-queue job for processing
                    </Text>
                  </InlineStack>
                )}
                {availableActions.canCancel && (
                  <InlineStack gap="100" blockAlign="center">
                    <XIcon />
                    <Text variant="bodySm" tone="subdued" as="span">
                      Cancel: Stop job execution
                    </Text>
                  </InlineStack>
                )}
                {availableActions.canRefresh && (
                  <InlineStack gap="100" blockAlign="center">
                    <RefreshIcon />
                    <Text variant="bodySm" tone="subdued" as="span">
                      Refresh: Update job status
                    </Text>
                  </InlineStack>
                )}
                {!availableActions.canRetry && !availableActions.canCancel && !availableActions.canRefresh && (
                  <Text variant="bodySm" tone="subdued" as="span">
                    No actions available for this job status
                  </Text>
                )}
              </BlockStack>
            </BlockStack>
          </Card>
        )}
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL STATUS INDICATOR - Visual status display
export interface JobStatusIndicatorProps {
  /** Job status */
  status: JobStatus;
  /** Show detailed info */
  detailed?: boolean;
}

export function JobStatusIndicator({ status, detailed = false }: JobStatusIndicatorProps) {
  const statusConfig = {
    pending: { 
      icon: ClockIcon, 
      tone: "info" as const, 
      label: "Pending",
      description: "Job is waiting to be processed"
    },
    processing: { 
      icon: PlayIcon, 
      tone: "warning" as const, 
      label: "Processing",
      description: "Job is currently being executed"
    },
    retrying: { 
      icon: RefreshIcon, 
      tone: "warning" as const, 
      label: "Retrying",
      description: "Job failed and is being retried"
    },
    completed: { 
      icon: PlayIcon, 
      tone: "success" as const, 
      label: "Completed",
      description: "Job finished successfully"
    },
    failed: { 
      icon: XIcon, 
      tone: "critical" as const, 
      label: "Failed",
      description: "Job failed and cannot be retried"
    },
    canceled: { 
      icon: XIcon, 
      tone: "subdued" as const, 
      label: "Canceled",
      description: "Job was canceled by user"
    },
    scheduled: { 
      icon: ClockIcon, 
      tone: "info" as const, 
      label: "Scheduled",
      description: "Job is scheduled for future execution"
    }
  };

  const config = statusConfig[status] || statusConfig.pending;

  return (
    <InlineStack gap="100" blockAlign="center">
      <Icon source={config.icon} tone={config.tone} />
      <BlockStack gap="050">
        <Text variant="bodyMd" fontWeight="medium" as="span">
          {config.label}
        </Text>
        {detailed && (
          <Text variant="bodySm" tone="subdued" as="span">
            {config.description}
          </Text>
        )}
      </BlockStack>
    </InlineStack>
  );
}

// 🦠 BACTERIAL HOOK - Job actions state management
export function useJobActions(jobId: string, initialStatus: JobStatus) {
  const availableActions = getAvailableActions(initialStatus);
  
  return {
    availableActions,
    canRetry: availableActions.canRetry,
    canCancel: availableActions.canCancel,
    canRefresh: availableActions.canRefresh,
    primaryAction: availableActions.primaryAction
  };
}
