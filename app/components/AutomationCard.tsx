// 🦠 BACTERIAL AUTOMATION CARD COMPONENT
// Self-contained, reusable automation management card

import { Card, BlockStack, InlineStack, Text, Button, Badge, Icon, Toolt<PERSON>, Spinner } from "@shopify/polaris";
import { PlayIcon, SettingsIcon, ClockIcon, CalendarIcon, XIcon } from "@shopify/polaris-icons";
import type { JobType } from "@prisma/client";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface AutomationCardProps {
  /** Automation ID */
  id: number;
  /** Automation name */
  name: string;
  /** Automation trigger description */
  trigger: string;
  /** Automation status */
  status: string;
  /** Automation type */
  type: JobType;
  /** Last run timestamp */
  lastRunAt: Date | null;
  /** Whether automation is currently running */
  running: boolean;
  /** Configuration object */
  config?: any;
  /** Configure URL */
  configureUrl?: string;
  /** Link component to use */
  LinkComponent?: any;
  /** Toggle status handler */
  onToggleStatus: (id: number, currentStatus: string) => void;
  /** Run bulk operation handler */
  onRunBulk?: (id: number) => void;
  /** Whether bulk run is starting */
  isStarting?: boolean;
  /** Whether any action is in progress */
  isActionInProgress?: boolean;

}

// 🦠 BACTERIAL STATUS MAPPING - Pure configuration
const STATUS_CONFIG = {
  Active: { tone: "success" as const, icon: PlayIcon },
  Inactive: { tone: "critical" as const, icon: XIcon },
  Paused: { tone: "warning" as const, icon: XIcon }
};

// 🦠 BACTERIAL BULK RUNNABLE TYPES - Pure configuration
const BULK_RUNNABLE_TYPES = [
  "AUTO_TAG_ORDERS_UTM",
  "ORDER_COLLECTION_TAG", 
  "COLLECTION_VISIBILITY_UPDATE",
  "AUTO_TAG_CUSTOMER_BY_VENDOR"
] as const;

// 🦠 BACTERIAL UTILITY - Check if automation supports bulk run
export function isBulkRunnable(type: JobType): boolean {
  return BULK_RUNNABLE_TYPES.includes(type as any);
}

// 🦠 BACTERIAL UTILITY - Format last run time
export function formatLastRun(lastRunAt: Date | null): string {
  if (!lastRunAt) return "Never";
  
  const now = new Date();
  const diffMs = now.getTime() - lastRunAt.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) return "Just now";
  if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
  
  return lastRunAt.toLocaleDateString();
}

// 🦠 BACTERIAL UTILITY - Extract delay info from config
export function getDelayInfo(config: any): string | null {
  if (!config || typeof config !== 'object') return null;
  if (!config.delay || typeof config.delay !== 'object') return null;
  if (config.delay.type === 'immediate') return null;
  
  if (config.delay.type === 'relative' && config.delay.value && config.delay.unit) {
    return `${config.delay.value} ${config.delay.unit}`;
  }
  
  if (config.delay.timestamp) {
    return `Specific time: ${config.delay.timestamp}`;
  }
  
  return "Configured delay";
}

// 🦠 BACTERIAL AUTOMATION CARD - Enhanced, reusable
export function AutomationCard({
  id,
  name,
  trigger,
  status,
  type,
  lastRunAt,
  running,
  config,
  configureUrl,
  LinkComponent,
  onToggleStatus,
  onRunBulk,
  isStarting = false,
  isActionInProgress = false
}: AutomationCardProps) {
  const isActive = status === "Active";
  const statusConfig = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG] || STATUS_CONFIG.Inactive;
  const delayInfo = getDelayInfo(config);
  const canBulkRun = isActive && isBulkRunnable(type) && onRunBulk;
  
  // 🦠 BACTERIAL ACTION HANDLERS - Pure functions
  const handleToggle = () => {
    onToggleStatus(id, status);
  };
  
  const handleBulkRun = () => {
    if (onRunBulk) {
      onRunBulk(id);
    }
  };

  // 🦠 BACTERIAL CONFIGURE BUTTON - With optional Link wrapper
  const configureButton = (
    <Button
      variant="tertiary"
      size="slim"
      icon={SettingsIcon}
      onClick={!configureUrl ? () => window.location.href = `/app/automations/configure/${id}` : undefined}
    >
      Configure
    </Button>
  );

  const configureAction = configureUrl && LinkComponent ? (
    <LinkComponent to={configureUrl} style={{ textDecoration: 'none' }}>
      {configureButton}
    </LinkComponent>
  ) : configureButton;

  return (
    <Card>
      <BlockStack gap="300">
        {/* Header with name and status */}
        <InlineStack align="space-between" blockAlign="start">
          <InlineStack gap="200" blockAlign="center">
            <Text variant="headingMd" as="h3">
              {name}
            </Text>
            {running && (
              <Tooltip content="Automation is currently running">
                <Spinner size="small" />
              </Tooltip>
            )}
          </InlineStack>
          
          <Badge tone={statusConfig.tone} icon={statusConfig.icon}>
            {status}
          </Badge>
        </InlineStack>

        {/* Automation details */}
        <BlockStack gap="200">
          <InlineStack gap="200" blockAlign="center">
            <Icon source={PlayIcon} tone="subdued" />
            <Text variant="bodySm" tone="subdued" as="span">
              Trigger: {trigger}
            </Text>
          </InlineStack>

          <InlineStack gap="200" blockAlign="center">
            <Icon source={ClockIcon} tone="subdued" />
            <Text variant="bodySm" tone="subdued" as="span">
              Last Run: {formatLastRun(lastRunAt)}
            </Text>
          </InlineStack>

          {delayInfo && (
            <InlineStack gap="200" blockAlign="center">
              <Icon source={CalendarIcon} tone="subdued" />
              <Text variant="bodySm" tone="subdued" as="span">
                Scheduled Delay: {delayInfo}
              </Text>
            </InlineStack>
          )}
        </BlockStack>

        {/* Actions */}
        <InlineStack gap="200" align="space-between">
          <InlineStack gap="200">
            {canBulkRun && (
              <Button
                variant="secondary"
                size="slim"
                onClick={handleBulkRun}
                disabled={running || isActionInProgress}
                loading={isStarting}
              >
                {isStarting ? "Starting..." : "Run Now"}
              </Button>
            )}
            
            <Button
              variant={isActive ? "tertiary" : "primary"}
              size="slim"
              onClick={handleToggle}
              disabled={running || isActionInProgress}
              tone={isActive ? "critical" : "success"}
            >
              {isActive ? "Disable" : "Enable"}
            </Button>
          </InlineStack>
          
          {configureAction}
        </InlineStack>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL AUTOMATION GRID - Simple wrapper
export interface AutomationGridProps {
  /** Array of automations */
  automations: Array<AutomationCardProps>;
}

export function AutomationGrid({ automations }: AutomationGridProps) {
  return (
    <BlockStack gap="400">
      {automations.map(automation => (
        <AutomationCard key={automation.id} {...automation} />
      ))}
    </BlockStack>
  );
}

// 🦠 BACTERIAL EMPTY STATE - Reusable empty state
export interface EmptyAutomationsProps {
  /** Custom message */
  message?: string;
  /** Action button text */
  actionText?: string;
  /** Action URL */
  actionUrl?: string;
  /** Link component */
  LinkComponent?: any;
}

export function EmptyAutomations({ 
  message = "You haven't created any automations yet.",
  actionText = "Browse Task Library",
  actionUrl = "/app/library",
  LinkComponent
}: EmptyAutomationsProps) {
  const actionButton = (
    <Button variant="primary">
      {actionText}
    </Button>
  );

  const action = actionUrl && LinkComponent ? (
    <LinkComponent to={actionUrl} style={{ textDecoration: 'none' }}>
      {actionButton}
    </LinkComponent>
  ) : actionButton;

  return (
    <Card>
      <BlockStack gap="400" align="center">
        <BlockStack gap="200" align="center">
          <Text variant="headingMd" as="h3" alignment="center">
            No automations yet
          </Text>
          <Text variant="bodyMd" as="p" alignment="center" tone="subdued">
            {message}
          </Text>
        </BlockStack>
        {action}
      </BlockStack>
    </Card>
  );
}
