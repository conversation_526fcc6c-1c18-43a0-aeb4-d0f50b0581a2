// 🦠 BACTERIAL RECENT ACTIVITY COMPONENTS
// Self-contained, reusable activity feed and timeline

import { Card, BlockStack, InlineStack, Text, Badge, Icon, Spin<PERSON>, <PERSON><PERSON> } from "@shopify/polaris";
import { ClockIcon, PlayIcon, CheckIcon, AlertTriangleIcon, RefreshIcon, ViewIcon } from "@shopify/polaris-icons";
import type { JobStatus, JobType } from "@prisma/client";
import { formatJobName } from "./JobCard";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface ActivityItemProps {
  /** Job ID */
  id: string;
  /** Job type */
  type: JobType;
  /** Job status */
  status: JobStatus;
  /** Creation date */
  createdAt: Date;
  /** Start date */
  startedAt?: Date | null;
  /** Completion date */
  completedAt?: Date | null;
  /** Error message */
  errorMessage?: string | null;
  /** Click handler */
  onClick?: (id: string) => void;
  /** Enhanced features */
  enhanced?: boolean;
  /** Link component */
  LinkComponent?: any;
  /** Details URL */
  detailsUrl?: string;
}

export interface ActivityFeedProps {
  /** Array of activity items */
  activities: ActivityItemProps[];
  /** Show empty state */
  showEmptyState?: boolean;
  /** Empty state message */
  emptyMessage?: string;
  /** Enhanced features */
  enhanced?: boolean;
  /** Maximum items to show */
  maxItems?: number;
}

export interface ActivityTimelineProps {
  /** Array of activity items */
  activities: ActivityItemProps[];
  /** Show relative times */
  showRelativeTimes?: boolean;
  /** Enhanced features */
  enhanced?: boolean;
}

// 🦠 BACTERIAL UTILITY - Format relative time
export function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) return "Just now";
  if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
  
  return date.toLocaleDateString();
}

// 🦠 BACTERIAL UTILITY - Get status config
export function getActivityStatusConfig(status: JobStatus): {
  icon: any;
  tone: "success" | "critical" | "warning" | "info";
  label: string;
  showSpinner: boolean;
} {
  switch (status) {
    case 'completed':
      return {
        icon: CheckIcon,
        tone: 'success',
        label: 'Completed',
        showSpinner: false
      };
    case 'failed':
      return {
        icon: AlertTriangleIcon,
        tone: 'critical',
        label: 'Failed',
        showSpinner: false
      };
    case 'processing':
    case 'retrying':
      return {
        icon: PlayIcon,
        tone: 'warning',
        label: status === 'retrying' ? 'Retrying' : 'Processing',
        showSpinner: true
      };
    case 'pending':
    case 'scheduled':
      return {
        icon: ClockIcon,
        tone: 'info',
        label: status === 'scheduled' ? 'Scheduled' : 'Pending',
        showSpinner: false
      };
    case 'canceled':
      return {
        icon: AlertTriangleIcon,
        tone: 'info',
        label: 'Canceled',
        showSpinner: false
      };
    default:
      return {
        icon: ClockIcon,
        tone: 'info',
        label: status,
        showSpinner: false
      };
  }
}

// 🦠 BACTERIAL UTILITY - Get job duration
export function getJobDuration(startedAt: Date | null, completedAt: Date | null): string | null {
  if (!startedAt) return null;
  
  const endTime = completedAt || new Date();
  const durationMs = endTime.getTime() - startedAt.getTime();
  const durationSecs = Math.floor(durationMs / 1000);
  
  if (durationSecs < 60) return `${durationSecs}s`;
  
  const durationMins = Math.floor(durationSecs / 60);
  if (durationMins < 60) return `${durationMins}m ${durationSecs % 60}s`;
  
  const durationHours = Math.floor(durationMins / 60);
  return `${durationHours}h ${durationMins % 60}m`;
}

// 🦠 BACTERIAL ACTIVITY ITEM - Enhanced activity display
export function ActivityItem({
  id,
  type,
  status,
  createdAt,
  startedAt,
  completedAt,
  errorMessage,
  onClick,
  enhanced = true,
  LinkComponent,
  detailsUrl
}: ActivityItemProps) {
  const statusConfig = getActivityStatusConfig(status);
  const jobName = formatJobName(type);
  const duration = getJobDuration(startedAt, completedAt);
  
  const handleClick = () => onClick?.(id);
  
  const content = (
    <Card>
      <BlockStack gap="200">
        {/* Header with job name and status */}
        <InlineStack align="space-between" blockAlign="start">
          <InlineStack gap="200" blockAlign="center">
            <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
              <Icon source={statusConfig.icon} tone={statusConfig.tone} />
            </div>
            <BlockStack gap="050">
              <Text variant="bodyMd" fontWeight="medium" as="h4">
                {jobName}
              </Text>
              <Text variant="bodySm" tone="subdued" as="span">
                {formatRelativeTime(createdAt)}
              </Text>
            </BlockStack>
          </InlineStack>
          
          <InlineStack gap="100" blockAlign="center">
            {statusConfig.showSpinner && (
              <Spinner size="small" />
            )}
            <Badge tone={statusConfig.tone}>
              {statusConfig.label}
            </Badge>
          </InlineStack>
        </InlineStack>
        
        {/* Enhanced details */}
        {enhanced && (
          <BlockStack gap="100">
            {duration && (
              <InlineStack gap="100" blockAlign="center">
                <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
                  <Icon source={ClockIcon} tone="subdued" />
                </div>
                <Text variant="bodySm" tone="subdued" as="span">
                  Duration: {duration}
                </Text>
              </InlineStack>
            )}
            
            {errorMessage && (
              <InlineStack gap="100" blockAlign="start">
                <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
                  <Icon source={AlertTriangleIcon} tone="critical" />
                </div>
                <Text variant="bodySm" tone="critical" as="span">
                  {errorMessage.length > 100 ? `${errorMessage.substring(0, 100)}...` : errorMessage}
                </Text>
              </InlineStack>
            )}
          </BlockStack>
        )}
        
        {/* Action button */}
        {(onClick || detailsUrl) && (
          <InlineStack align="end">
            <Button
              size="slim"
              variant="tertiary"
              icon={ViewIcon}
              onClick={!detailsUrl ? handleClick : undefined}
            >
              View Details
            </Button>
          </InlineStack>
        )}
      </BlockStack>
    </Card>
  );
  
  // Wrap with Link if provided
  if (detailsUrl && LinkComponent) {
    return (
      <LinkComponent to={detailsUrl} style={{ textDecoration: 'none' }}>
        {content}
      </LinkComponent>
    );
  }
  
  return content;
}

// 🦠 BACTERIAL ACTIVITY FEED - Main activity display
export function ActivityFeed({
  activities,
  showEmptyState = true,
  emptyMessage = "No recent activity to display",
  enhanced = true,
  maxItems = 10
}: ActivityFeedProps) {
  const displayActivities = activities.slice(0, maxItems);
  
  if (activities.length === 0 && showEmptyState) {
    return (
      <Card>
        <BlockStack gap="400" align="center">
          <BlockStack gap="200" align="center">
            <Icon source={ClockIcon} tone="subdued" />
            <Text variant="headingMd" as="h3" alignment="center">
              No Recent Activity
            </Text>
            <Text variant="bodyMd" as="p" alignment="center" tone="subdued">
              {emptyMessage}
            </Text>
          </BlockStack>
        </BlockStack>
      </Card>
    );
  }
  
  return (
    <BlockStack gap="300">
      {displayActivities.map((activity) => (
        <ActivityItem key={activity.id} {...activity} enhanced={enhanced} />
      ))}
      
      {activities.length > maxItems && (
        <Card>
          <InlineStack align="center">
            <Text variant="bodySm" tone="subdued" as="p">
              Showing {maxItems} of {activities.length} activities
            </Text>
          </InlineStack>
        </Card>
      )}
    </BlockStack>
  );
}

// 🦠 BACTERIAL ACTIVITY TIMELINE - Compact timeline view
export function ActivityTimeline({
  activities,
  showRelativeTimes = true,
  enhanced = true
}: ActivityTimelineProps) {
  if (activities.length === 0) {
    return (
      <Card>
        <Text variant="bodySm" tone="subdued" as="p" alignment="center">
          No activity to display
        </Text>
      </Card>
    );
  }
  
  return (
    <Card>
      <BlockStack gap="300">
        <Text variant="headingMd" as="h3">
          Activity Timeline
        </Text>
        
        <BlockStack gap="200">
          {activities.map((activity, index) => {
            const statusConfig = getActivityStatusConfig(activity.status);
            const isLast = index === activities.length - 1;
            
            return (
              <InlineStack key={activity.id} gap="200" blockAlign="start">
                {/* Timeline indicator */}
                <div style={{ 
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center',
                  minWidth: '20px'
                }}>
                  <div style={{
                    width: '20px',
                    height: '20px',
                    borderRadius: '50%',
                    backgroundColor: statusConfig.tone === 'success' ? 'var(--p-color-bg-success)' : 
                                   statusConfig.tone === 'critical' ? 'var(--p-color-bg-critical)' :
                                   statusConfig.tone === 'warning' ? 'var(--p-color-bg-warning)' :
                                   'var(--p-color-bg-info)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <Icon source={statusConfig.icon} tone={statusConfig.tone} />
                  </div>
                  {!isLast && (
                    <div style={{
                      width: '2px',
                      height: '30px',
                      backgroundColor: 'var(--p-color-border-subdued)',
                      marginTop: '4px'
                    }} />
                  )}
                </div>
                
                {/* Activity content */}
                <BlockStack gap="050" style={{ flex: 1 }}>
                  <InlineStack align="space-between" blockAlign="center">
                    <Text variant="bodyMd" fontWeight="medium" as="span">
                      {formatJobName(activity.type)}
                    </Text>
                    <Badge tone={statusConfig.tone} size="small">
                      {statusConfig.label}
                    </Badge>
                  </InlineStack>
                  
                  <Text variant="bodySm" tone="subdued" as="span">
                    {showRelativeTimes ? formatRelativeTime(activity.createdAt) : activity.createdAt.toLocaleString()}
                  </Text>
                  
                  {enhanced && activity.errorMessage && (
                    <Text variant="bodySm" tone="critical" as="span">
                      {activity.errorMessage.length > 60 ? `${activity.errorMessage.substring(0, 60)}...` : activity.errorMessage}
                    </Text>
                  )}
                </BlockStack>
              </InlineStack>
            );
          })}
        </BlockStack>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL HOOK - Recent activity state management
export function useRecentActivity(rawActivities: any[]) {
  const activities: ActivityItemProps[] = rawActivities.map(activity => ({
    id: activity.id,
    type: activity.type,
    status: activity.status,
    createdAt: new Date(activity.createdAt),
    startedAt: activity.startedAt ? new Date(activity.startedAt) : null,
    completedAt: activity.completedAt ? new Date(activity.completedAt) : null,
    errorMessage: activity.errorMessage,
    detailsUrl: `/app/jobs/${activity.id}`,
    enhanced: true
  }));
  
  return { activities };
}
