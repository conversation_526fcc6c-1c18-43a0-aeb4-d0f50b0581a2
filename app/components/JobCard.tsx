// 🦠 BACTERIAL JOB CARD COMPONENT
// Self-contained, reusable job management card

import { Card, BlockStack, InlineStack, Text, Button, Badge, Icon, Toolt<PERSON>, Spinner } from "@shopify/polaris";
import { PlayIcon, RefreshIcon, XIcon, ClockIcon, CalendarIcon, AlertTriangleIcon } from "@shopify/polaris-icons";
import type { JobStatus, JobType } from "@prisma/client";

// 🦠 BACTERIAL INTERFACES - Pure, self-contained
export interface JobCardProps {
  /** Job ID */
  id: string;
  /** Job type */
  type: JobType;
  /** Job status */
  status: JobStatus;
  /** Job creation date */
  createdAt: Date;
  /** Job start date */
  startedAt: Date | null;
  /** Job completion date */
  completedAt: Date | null;
  /** Job scheduled date */
  scheduledAt: Date | null;
  /** Error message if failed */
  errorMessage: string | null;
  /** Job data payload */
  data: string;
  /** Retry job handler */
  onRetry: (jobId: string) => void;
  /** Cancel job handler */
  onCancel: (jobId: string) => void;
  /** View details handler */
  onViewDetails?: (jobId: string) => void;
  /** Whether action is in progress */
  isActionInProgress?: boolean;
  /** Link component for navigation */
  LinkComponent?: any;
  /** Details URL */
  detailsUrl?: string;
  /** Show enhanced features */
  enhanced?: boolean;
}

// 🦠 BACTERIAL STATUS CONFIGURATION - Pure mapping
const STATUS_CONFIG = {
  pending: { tone: "info" as const, icon: ClockIcon, label: "Pending" },
  processing: { tone: "warning" as const, icon: PlayIcon, label: "Processing" },
  completed: { tone: "success" as const, icon: PlayIcon, label: "Completed" },
  failed: { tone: "critical" as const, icon: AlertTriangleIcon, label: "Failed" },
  retrying: { tone: "warning" as const, icon: RefreshIcon, label: "Retrying" },
  canceled: { tone: "info" as const, icon: XIcon, label: "Canceled" },
  scheduled: { tone: "info" as const, icon: CalendarIcon, label: "Scheduled" }
};

// 🦠 BACTERIAL UTILITY - Format job name
export function formatJobName(type: JobType): string {
  const jobNames: Record<JobType, string> = {
    AUTO_TAG_ORDERS_UTM: "UTM Order Tagging",
    ORDER_COLLECTION_TAG: "Collection Order Tagging",
    COLLECTION_VISIBILITY_UPDATE: "Collection Visibility Update",
    ORDER_CART_ATTRIBUTE_TAG: "Cart Attribute Tagging",
    AUTO_TAG_CUSTOMER_BY_ORDER_TAG: "Customer Order Tag Tagging",
    AUTO_TAG_CUSTOMER_BY_VENDOR: "Customer Vendor Tagging",
    COLLECTION_VISIBILITY_BULK_UPDATE: "Bulk Collection Visibility Update",
    STUCK_JOB_CLEANUP: "Stuck Job Cleanup",
    ORDER_DISCOUNT_TAG: "Order Discount Tagging",
    CANCEL_HIGH_RISK_ORDER: "Cancel High Risk Orders"
  };
  return jobNames[type] || type;
}

// 🦠 BACTERIAL UTILITY - Format relative time
export function formatRelativeTime(date: Date): string {
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) return "Just now";
  if (diffMins < 60) return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  if (diffDays < 7) return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
  
  return date.toLocaleDateString();
}

// 🦠 BACTERIAL UTILITY - Get job duration
export function getJobDuration(startedAt: Date | null, completedAt: Date | null): string | null {
  if (!startedAt) return null;
  
  const endTime = completedAt || new Date();
  const durationMs = endTime.getTime() - startedAt.getTime();
  const durationSecs = Math.floor(durationMs / 1000);
  
  if (durationSecs < 60) return `${durationSecs}s`;
  
  const durationMins = Math.floor(durationSecs / 60);
  if (durationMins < 60) return `${durationMins}m ${durationSecs % 60}s`;
  
  const durationHours = Math.floor(durationMins / 60);
  return `${durationHours}h ${durationMins % 60}m`;
}

// 🦠 BACTERIAL UTILITY - Parse job data safely
export function parseJobData(data: string): Record<string, any> | null {
  try {
    return JSON.parse(data);
  } catch {
    return null;
  }
}

// 🦠 BACTERIAL JOB CARD - Enhanced, reusable
export function JobCard({
  id,
  type,
  status,
  createdAt,
  startedAt,
  completedAt,
  scheduledAt,
  errorMessage,
  data,
  onRetry,
  onCancel,
  onViewDetails,
  isActionInProgress = false,
  LinkComponent,
  detailsUrl,
  enhanced = true
}: JobCardProps) {
  const statusConfig = STATUS_CONFIG[status] || STATUS_CONFIG.pending;
  const jobName = formatJobName(type);
  const duration = getJobDuration(startedAt, completedAt);
  const jobData = parseJobData(data);
  
  // 🦠 BACTERIAL ACTION HANDLERS - Pure functions
  const handleRetry = () => onRetry(id);
  const handleCancel = () => onCancel(id);
  const handleViewDetails = () => onViewDetails?.(id);

  // 🦠 BACTERIAL DETAILS BUTTON - With optional Link wrapper
  const detailsButton = (
    <Button
      variant="tertiary"
      size="slim"
      onClick={!detailsUrl ? handleViewDetails : undefined}
    >
      View Details
    </Button>
  );

  const detailsAction = detailsUrl && LinkComponent ? (
    <LinkComponent to={detailsUrl} style={{ textDecoration: 'none' }}>
      {detailsButton}
    </LinkComponent>
  ) : detailsButton;

  return (
    <Card>
      <BlockStack gap="300">
        {/* Header with job name and status */}
        <InlineStack align="space-between" blockAlign="start">
          <InlineStack gap="200" blockAlign="center">
            <Text variant="headingMd" as="h3">
              {jobName}
            </Text>
            {status === 'processing' && (
              <Tooltip content="Job is currently running">
                <Spinner size="small" />
              </Tooltip>
            )}
          </InlineStack>
          
          <Badge tone={statusConfig.tone} icon={statusConfig.icon}>
            {statusConfig.label}
          </Badge>
        </InlineStack>

        {/* Job timing information */}
        <BlockStack gap="100">
          <InlineStack gap="100" blockAlign="center" wrap={false}>
            <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
              <Icon source={ClockIcon} tone="subdued" />
            </div>
            <Text variant="bodySm" tone="subdued" as="span">
              Created: {formatRelativeTime(createdAt)}
            </Text>
          </InlineStack>
          
          {startedAt && (
            <InlineStack gap="100" blockAlign="center" wrap={false}>
              <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
                <Icon source={PlayIcon} tone="subdued" />
              </div>
              <Text variant="bodySm" tone="subdued" as="span">
                Started: {startedAt.toLocaleString()}
              </Text>
            </InlineStack>
          )}
          
          {completedAt && (
            <InlineStack gap="100" blockAlign="center" wrap={false}>
              <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
                <Icon source={PlayIcon} tone="subdued" />
              </div>
              <Text variant="bodySm" tone="subdued" as="span">
                Completed: {completedAt.toLocaleString()}
                {duration && ` (${duration})`}
              </Text>
            </InlineStack>
          )}
          
          {scheduledAt && (
            <InlineStack gap="100" blockAlign="center" wrap={false}>
              <div style={{ minWidth: '16px', display: 'flex', alignItems: 'center' }}>
                <Icon source={CalendarIcon} tone="subdued" />
              </div>
              <Text variant="bodySm" tone="subdued" as="span">
                Scheduled: {scheduledAt.toLocaleString()}
              </Text>
            </InlineStack>
          )}
        </BlockStack>

        {/* Error message */}
        {errorMessage && (
          <Card background="bg-surface-critical">
            <InlineStack gap="100" blockAlign="start">
              <Icon source={AlertTriangleIcon} tone="critical" />
              <Text variant="bodySm" tone="critical" as="span">
                <strong>Error:</strong> {errorMessage}
              </Text>
            </InlineStack>
          </Card>
        )}

        {/* Job data preview (if enhanced) */}
        {enhanced && jobData && Object.keys(jobData).length > 0 && (
          <Card background="bg-surface-secondary">
            <Text variant="bodySm" tone="subdued" as="p">
              <strong>Data:</strong> {Object.keys(jobData).length} parameter{Object.keys(jobData).length === 1 ? '' : 's'}
            </Text>
          </Card>
        )}

        {/* Actions */}
        <InlineStack gap="200" align="space-between">
          <InlineStack gap="200">
            {status === 'failed' && (
              <Button
                variant="secondary"
                size="slim"
                onClick={handleRetry}
                disabled={isActionInProgress}
                icon={RefreshIcon}
              >
                Retry
              </Button>
            )}
            
            {status === 'processing' && (
              <Button
                variant="secondary"
                size="slim"
                onClick={handleCancel}
                disabled={isActionInProgress}
                tone="critical"
                icon={XIcon}
              >
                Cancel
              </Button>
            )}
          </InlineStack>
          
          {(onViewDetails || detailsUrl) && detailsAction}
        </InlineStack>
      </BlockStack>
    </Card>
  );
}

// 🦠 BACTERIAL JOB GRID - Simple wrapper
export interface JobGridProps {
  /** Array of jobs */
  jobs: Array<JobCardProps>;
  /** Enhanced card features */
  enhanced?: boolean;
}

export function JobGrid({ jobs, enhanced = true }: JobGridProps) {
  return (
    <BlockStack gap="400">
      {jobs.map(job => (
        <JobCard key={job.id} {...job} enhanced={enhanced} />
      ))}
    </BlockStack>
  );
}

// 🦠 BACTERIAL EMPTY STATE - Reusable empty state
export interface EmptyJobsProps {
  /** Custom message */
  message?: string;
  /** Action button text */
  actionText?: string;
  /** Action handler */
  onAction?: () => void;
  /** Show clear filters action */
  showClearFilters?: boolean;
}

export function EmptyJobs({ 
  message = "No jobs found matching your criteria.",
  actionText = "Clear Filters",
  onAction,
  showClearFilters = true
}: EmptyJobsProps) {
  return (
    <Card>
      <BlockStack gap="400" align="center">
        <BlockStack gap="200" align="center">
          <Text variant="headingMd" as="h3" alignment="center">
            No jobs found
          </Text>
          <Text variant="bodyMd" as="p" alignment="center" tone="subdued">
            {message}
          </Text>
        </BlockStack>
        {showClearFilters && onAction && (
          <Button variant="secondary" onClick={onAction}>
            {actionText}
          </Button>
        )}
      </BlockStack>
    </Card>
  );
}
