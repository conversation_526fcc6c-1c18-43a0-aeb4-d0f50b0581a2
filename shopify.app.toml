# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "59ad5eb07945ff6f97fe743598978dce"
application_url = "https://charms-encounter-lite-slot.trycloudflare.com"
embedded = true
name = "AutomateTheBoringStuffs"
handle = "automatetheboringstuffs"

[build]
include_config_on_deploy = true
automatically_update_urls_on_dev = true

[webhooks]
api_version = "2025-04"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  [[webhooks.subscriptions]]
  topics = [ "inventory_levels/update" ]
  uri = "/api/webhooks/inventory-update"

  [[webhooks.subscriptions]]
  topics = [ "orders/create" ]
  uri = "/api/webhooks/orders-utm-tagging"

  [[webhooks.subscriptions]]
  topics = [ "bulk_operations/finish" ]
  uri = "/api/webhooks/bulk-operations-finish"

  [[webhooks.subscriptions]]
  topics = [ "orders/create" ]
  uri = "/api/webhooks/orders-collection-tagging"

  [[webhooks.subscriptions]]
  topics = [ "orders/create", "orders/updated" ]
  uri = "/api/webhooks/orders-cart-attribute-tagging"

  [[webhooks.subscriptions]]
  topics = [ "orders/create", "orders/updated" ]
  uri = "/api/webhooks/orders-customer-tagging"

  [[webhooks.subscriptions]]
  topics = [ "orders/create" ]
  uri = "/api/webhooks/orders-customer-vendor-tagging"

  [[webhooks.subscriptions]]
  topics = [ "orders/create" ]
  uri = "/api/webhooks/orders-discount-tagging"

  [[webhooks.subscriptions]]
  topics = [ "orders/create", "orders/updated" ]
  uri = "/api/webhooks/orders-cancel-high-risk"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_products,read_products,read_inventory,write_publications,read_product_listings,read_checkouts,read_customers,write_customers,read_orders,write_orders,read_price_rules"

[auth]
redirect_urls = [
  "https://charms-encounter-lite-slot.trycloudflare.com/auth/callback",
  "https://charms-encounter-lite-slot.trycloudflare.com/auth/shopify/callback",
  "https://charms-encounter-lite-slot.trycloudflare.com/api/auth/callback"
]

[pos]
embedded = false
